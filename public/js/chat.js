// chat.js
const socket = io()

// Check if user has a username, redirect if not
const username = localStorage.getItem('username')
if (!username) {
  window.location.href = 'index.html'
} else {
  document.getElementById('welcomeText').innerText = `Welcome, ${username}!`
}

function sendMessage() {
  const input = document.getElementById('messageInput')
  const messageText = input.value.trim()

  if (!messageText) return

  const sendButton = document.getElementById('sendButton')
  const sendText = document.getElementById('sendText')
  const sendSpinner = document.getElementById('sendSpinner')

  // Show loading state
  sendButton.disabled = true
  sendText.style.display = 'none'
  sendSpinner.style.display = 'inline-block'

  const message = `${username}: ${messageText}`
  socket.emit('chatMessage', message)

  // Reset input and button state
  input.value = ''
  setTimeout(() => {
    sendButton.disabled = false
    sendText.style.display = 'inline'
    sendSpinner.style.display = 'none'
    input.focus()
  }, 300)
}

// Listen for incoming messages
socket.on('message', message => {
  const chatBox = document.getElementById('chatBox')
  const messageDiv = document.createElement('div')
  messageDiv.className = 'message'

  // Parse message to separate username and content
  const colonIndex = message.indexOf(': ')
  if (colonIndex !== -1) {
    const messageUsername = message.substring(0, colonIndex)
    const messageContent = message.substring(colonIndex + 2)
    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

    messageDiv.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: flex-start;">
        <div>
          <strong style="color: #667eea;">${messageUsername}</strong>
          <div style="margin-top: 0.25rem;">${messageContent}</div>
        </div>
        <span style="font-size: 0.8rem; color: #a0aec0; margin-left: 1rem;">${timestamp}</span>
      </div>
    `
  } else {
    messageDiv.innerText = message
  }

  chatBox.appendChild(messageDiv)
  chatBox.scrollTop = chatBox.scrollHeight

  // Add notification sound (optional)
  if (message.includes(username) && !message.startsWith(username + ':')) {
    // User was mentioned
    messageDiv.style.background = '#fef5e7'
    messageDiv.style.borderLeftColor = '#f6ad55'
  }
})

// Handle connection events
socket.on('connect', () => {
  console.log('Connected to server')
})

socket.on('disconnect', () => {
  console.log('Disconnected from server')
})
