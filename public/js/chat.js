// chat.js
const socket = io()

const username = localStorage.getItem('username') || 'Anonymous'
document.getElementById('welcomeText').innerText = `Welcome, ${username}!`

function sendMessage() {
  const input = document.getElementById('messageInput')
  const message = `${username}: ${input.value}`
  socket.emit('chatMessage', message)
  input.value = ''
}

// Listen for incoming messages
socket.on('message', message => {
  const chatBox = document.getElementById('chatBox')
  const div = document.createElement('div')
  div.innerText = message
  chatBox.appendChild(div)
  chatBox.scrollTop = chatBox.scrollHeight
})
