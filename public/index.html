<!-- index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Simple Chat - Join the Conversation</title>
  <link rel="stylesheet" href="css/styles.css" />
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>" />
</head>
<body>
  <div class="container">
    <div class="text-center mb-1">
      <div style="font-size: 3rem; margin-bottom: 1rem;">💬</div>
      <h1>Welcome to Simple Chat</h1>
      <p style="color: #718096; margin-bottom: 2rem;">Enter your name to join the conversation</p>
    </div>

    <div class="input-group">
      <input
        type="text"
        id="usernameInput"
        placeholder="Enter your username"
        maxlength="20"
        autocomplete="off"
      />
    </div>

    <button onclick="joinChat()" id="joinButton">
      <span id="buttonText">Join Chat</span>
      <span id="loadingSpinner" class="loading" style="display: none;"></span>
    </button>

    <div style="margin-top: 2rem; font-size: 0.9rem; color: #718096;">
      <p>✨ Real-time messaging</p>
      <p>🌐 Connect with others instantly</p>
    </div>
  </div>

  <script>
    // Add enter key support
    document.getElementById('usernameInput').addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        joinChat();
      }
    });

    function joinChat() {
      const username = document.getElementById('usernameInput').value.trim();
      const button = document.getElementById('joinButton');
      const buttonText = document.getElementById('buttonText');
      const loadingSpinner = document.getElementById('loadingSpinner');

      if (!username) {
        // Add shake animation for empty input
        const input = document.getElementById('usernameInput');
        input.style.animation = 'shake 0.5s ease-in-out';
        input.focus();
        setTimeout(() => {
          input.style.animation = '';
        }, 500);
        return;
      }

      // Show loading state
      button.disabled = true;
      buttonText.style.display = 'none';
      loadingSpinner.style.display = 'inline-block';

      // Simulate brief loading for better UX
      setTimeout(() => {
        localStorage.setItem('username', username);
        window.location.href = 'chat.html';
      }, 800);
    }
  </script>

  <style>
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }
  </style>
</body>
</html>
