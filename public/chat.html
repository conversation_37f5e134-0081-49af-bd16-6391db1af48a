<!-- chat.html -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Simple Chat - Chat Room</title>
  <link rel="stylesheet" href="css/styles.css" />
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>" />
</head>
<body>
  <div class="chat-container">
    <div class="chat-header">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <div>
          <span style="font-size: 1.5rem;">💬</span>
          <h2 id="welcomeText" style="display: inline; margin: 0 0 0 0.5rem;"></h2>
        </div>
        <div>
          <button
            onclick="leaveChat()"
            style="background: #e53e3e; padding: 0.5rem 1rem; font-size: 0.9rem; width: auto; margin: 0;"
          >
            Leave Chat
          </button>
        </div>
      </div>
      <div id="connectionStatus" style="margin-top: 0.5rem; font-size: 0.9rem; color: #718096;">
        <span id="statusIndicator">🟢</span> Connected
      </div>
    </div>

    <div id="chatBox"></div>

    <div class="chat-input-area">
      <input
        type="text"
        id="messageInput"
        placeholder="Type your message..."
        maxlength="500"
        autocomplete="off"
      />
      <button onclick="sendMessage()" id="sendButton">
        <span id="sendText">Send</span>
        <span id="sendSpinner" class="loading" style="display: none;"></span>
      </button>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="js/chat.js"></script>

  <script>
    // Add enter key support for message input
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    // Leave chat function
    function leaveChat() {
      if (confirm('Are you sure you want to leave the chat?')) {
        localStorage.removeItem('username');
        window.location.href = 'index.html';
      }
    }

    // Connection status handling
    const socket = io();
    const statusIndicator = document.getElementById('statusIndicator');
    const connectionStatus = document.getElementById('connectionStatus');

    socket.on('connect', () => {
      statusIndicator.textContent = '🟢';
      connectionStatus.innerHTML = '<span id="statusIndicator">🟢</span> Connected';
    });

    socket.on('disconnect', () => {
      statusIndicator.textContent = '🔴';
      connectionStatus.innerHTML = '<span id="statusIndicator">🔴</span> Disconnected';
    });

    // Auto-focus message input
    document.getElementById('messageInput').focus();
  </script>
</body>
</html>
