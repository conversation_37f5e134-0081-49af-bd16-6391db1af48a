/* Modern Chat App Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}

.container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  width: 90%;
  max-width: 400px;
  text-align: center;
  animation: slideUp 0.6s ease-out;
}

.chat-container {
  max-width: 800px;
  width: 90%;
  height: 90vh;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h1, h2 {
  color: #4a5568;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

h1 {
  font-size: 1.8rem;
}

h2 {
  font-size: 1.5rem;
}

.input-group {
  margin-bottom: 1.5rem;
}

input[type="text"] {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8fafc;
}

input[type="text"]:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 1rem;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

button:active {
  transform: translateY(0);
}

/* Chat specific styles */
.chat-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px 20px 0 0;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

#chatBox {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  overflow-y: auto;
  border: none;
  margin: 0;
}

.message {
  background: #f8fafc;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  border-left: 4px solid #667eea;
  animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.chat-input-area {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 0 0 20px 20px;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  border-top: 1px solid #e2e8f0;
}

.chat-input-area input {
  flex: 1;
  margin: 0;
}

.chat-input-area button {
  width: auto;
  margin: 0;
  padding: 1rem 1.5rem;
  min-width: 80px;
}

/* Scrollbar styling */
#chatBox::-webkit-scrollbar {
  width: 6px;
}

#chatBox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

#chatBox::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 3px;
}

#chatBox::-webkit-scrollbar-thumb:hover {
  background: #5a67d8;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .chat-container {
    height: 95vh;
    margin: 1rem;
  }
  
  .chat-input-area {
    flex-direction: column;
  }
  
  .chat-input-area button {
    width: 100%;
  }
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 1rem;
}

.mt-1 {
  margin-top: 1rem;
}
