// server.js
const express = require('express')
const http = require('http')
const socketio = require('socket.io')

const app = express()
const server = http.createServer(app)
const io = socketio(server)

app.use(express.static('public'))

io.on('connection', socket => {
  console.log('User connected')

  socket.on('chatMessage', msg => {
    io.emit('message', msg) // Broadcast to all clients
  })

  socket.on('disconnect', () => {
    console.log('User disconnected')
  })
})

server.listen(3000, () => console.log('Server running on http://localhost:3000'))
